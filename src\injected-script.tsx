/**
 * Injected script that runs in the page context
 * This script has access to the page's window object and can intercept XMLHttpRequest
 */

import { render } from 'preact';
import { App } from './core/app';
import extensions from './core/extensions';

import BookmarksModule from './modules/bookmarks';
import DirectMessagesModule from './modules/direct-messages';
import HomeTimelineModule from './modules/home-timeline';
import LikesModule from './modules/likes';
import ListTimelineModule from './modules/list-timeline';
import RuntimeLogsModule from './modules/runtime-logs';
import SearchTimelineModule from './modules/search-timeline';
import TweetDetailModule from './modules/tweet-detail';
import UserMediaModule from './modules/user-media';
import UserTweetsModule from './modules/user-tweets';

import './index.css';

// Register all extensions
extensions.add(HomeTimelineModule);
extensions.add(ListTimelineModule);
extensions.add(BookmarksModule);
extensions.add(LikesModule);
extensions.add(UserTweetsModule);
extensions.add(UserMediaModule);
extensions.add(TweetDetailModule);
extensions.add(SearchTimelineModule);
extensions.add(DirectMessagesModule);
extensions.add(RuntimeLogsModule);
extensions.start();

function mountApp() {
  const root = document.createElement('div');
  root.id = 'twe-root';
  document.body.append(root);

  render(<App />, root);
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', mountApp);
} else {
  mountApp();
}
